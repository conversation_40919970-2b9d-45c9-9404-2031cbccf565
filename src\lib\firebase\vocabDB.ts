import { 
    collection, 
    doc, 
    getDoc, 
    getDocs, 
    addDoc, 
    updateDoc, 
    deleteDoc, 
    query, 
    orderBy, 
    runTransaction,
    onSnapshot,
    serverTimestamp,
    Timestamp,
    type Unsubscribe
} from 'firebase/firestore';
import { 
    createEmptyCard, 
    fsrs, 
    generatorParameters, 
    Rating, 
    State,
    type Card as FSRSCard,
    type RecordLogItem,
    type FSRS,
    type FSRSParameters
} from 'ts-fsrs';
import { db } from './firestore';
import type { 
    Deck, 
    Card, 
    VocabCard,
    StudySession, 
    DeckStats, 
    FSRSCardFireStore
} from '$lib/types/vocab.types';

// Collection references
const DECKS_COLLECTION = 'decks';
const VOCAB_COLLECTION = 'vocab';
const STUDY_SESSIONS_COLLECTION = 'studySessions';
const USER_PROGRESS_COLLECTION = 'userProgress';

// FSRS Configuration
const fsrsParams: FSRSParameters = generatorParameters({ 
    enable_fuzz: true,
    enable_short_term: true,
    maximum_interval: 365, // 1 year max
    request_retention: 0.9, // 90% retention rate
});
export const fsrsScheduler: FSRS = fsrs(fsrsParams);

// Helper function to get user's decks collection reference
const getUserDecksCollection = (userId: string) => collection(db, 'users', userId, DECKS_COLLECTION);

// Helper function to convert FSRS card to our Card interface
export function convertFSRSToCard(fsrsCard: FSRSCardFireStore, id: number, vocabCard: VocabCard): Card {
    return {
        id,
        vocabCard,
        fsrsCard
    };
}

// Helper function to convert our Card to FSRS card
export function convertCardToFSRS(card: Card): FSRSCard {
    return convertTimestampToDate(card).fsrsCard as FSRSCard;
}

// ============================================================================
// DECK OPERATIONS
// ============================================================================

/**
 * Create a new deck for a user
 */
export async function createDeck(
    userId: string, 
    name: string, 
    description: string = 'No description'
): Promise<string> {
    try {
        if (description === '') description = 'No description';
        const decksRef = getUserDecksCollection(userId);
        const newDeck: Omit<Deck, 'id'> = {
            name,
            description,
            createdAt: serverTimestamp() as Timestamp,
            updatedAt: serverTimestamp() as Timestamp,
            cards: []
        };
        
        const docRef = await addDoc(decksRef, newDeck);
        return docRef.id;
    } catch (error) {
        console.error('Error creating deck:', error);
        throw new Error('Failed to create deck');
    }
}

/**
 * Get all decks for a user
 */
export async function getUserDecks(userId: string): Promise<Deck[]> {
    try {
        const decksRef = getUserDecksCollection(userId);
        const q = query(decksRef, orderBy('updatedAt', 'desc'));
        const snapshot = await getDocs(q);
        
        return snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        } as Deck));
    } catch (error) {
        console.error('Error fetching user decks:', error);
        throw new Error('Failed to fetch decks');
    }
}

/**
 * Get a specific deck by ID
 */
export async function getDeck(userId: string, deckId: string): Promise<Deck | null> {
    try {
        const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
        const snapshot = await getDoc(deckRef);
        
        if (!snapshot.exists()) {
            return null;
        }
        
        return {
            id: snapshot.id,
            ...snapshot.data()
        } as Deck;
    } catch (error) {
        console.error('Error fetching deck:', error);
        throw new Error('Failed to fetch deck');
    }
}

/**
 * Update deck metadata (name, description)
 */
export async function updateDeck(
    userId: string,   
    deckId: string, 
    updates: Partial<Pick<Deck, 'name' | 'description'>>
): Promise<void> {
    try {
        const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
        await updateDoc(deckRef, {
            ...updates,
            updatedAt: serverTimestamp()
        });
    } catch (error) {
        console.error('Error updating deck:', error);
        throw new Error('Failed to update deck');
    }
}

/**
 * Delete a deck
 */
export async function deleteDeck(userId: string, deckId: string): Promise<void> {
    try {
        const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
        await deleteDoc(deckRef);
    } catch (error) {
        console.error('Error deleting deck:', error);
        throw new Error('Failed to delete deck');
    }
}

/**
 * Calculate deck statistics
 */
export function calculateDeckStats(cards: Card[]): DeckStats {
    const now = new Date();
    
    const stats: DeckStats = {
        total: cards?.length || 0,
        new: 0,
        learn: 0,
        review: 0,
        due: 0
    };
  
    cards?.forEach(card => {
        // Count by state
        switch (card.fsrsCard.state) {
            case State.New:
                stats.new++;
                break;
            case State.Learning:
            case State.Relearning:
                stats.learn++;
                break;
            case State.Review:
                stats.review++;
                break;
        }
        
        // Count due cards    
        const dateCard = convertTimestampToDate(card);
        const dueDate = dateCard.fsrsCard.due as Date;
        if (dueDate.getTime() <= now.getTime()) {
            stats.due++;
        }
    });
    
    return stats;
}

// ============================================================================
// CARD OPERATIONS
// ============================================================================

/**
 * Add a card to a deck using FSRS
 */
export async function addCardToDeck(
    userId: string, 
    deckId: string, 
    vocabCard: VocabCard
): Promise<void> {
    try {
        await runTransaction(db, async (transaction) => {
            const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
            const deckDoc = await transaction.get(deckRef);
            
            if (!deckDoc.exists()) {
                throw new Error('Deck not found');
            }
            
            const deck = deckDoc.data() as Deck;
            const nextCardId = Math.max(0, ...deck.cards.map(c => c.id)) + 1;
            
            // Create new FSRS card
            const fsrsCard = createEmptyCard();
            if (!fsrsCard.last_review) delete fsrsCard.last_review;
            const newCard = convertFSRSToCard(fsrsCard, nextCardId, vocabCard);
            
            transaction.update(deckRef, {
                cards: [...deck.cards, newCard],
                updatedAt: serverTimestamp()
            });
        });
    } catch (error) {
        console.error('Error adding card to deck:', error);
        throw new Error('Failed to add card to deck');
    }
}

/**
 * Review a card using FSRS algorithm
 */
export async function reviewCard(
    userId: string,
    deckId: string,
    cardId: number,
    rating: Rating,
    reviewDate: Date = new Date()
): Promise<RecordLogItem> {
    try {
        let recordLog: RecordLogItem;
        
        await runTransaction(db, async (transaction) => {
            const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
            const deckDoc = await transaction.get(deckRef);
            
            if (!deckDoc.exists()) {
                throw new Error('Deck not found');
            }
            
            const deck = deckDoc.data() as Deck;
            const cardIndex = deck.cards.findIndex(c => c.id === cardId);
            
            if (cardIndex === -1) {
                throw new Error('Card not found');
            }
            
            const currentCard = deck.cards[cardIndex];
            const fsrsCard = convertCardToFSRS(currentCard);
            
            // Use FSRS to calculate next review
            const schedulingCards = fsrsScheduler.repeat(fsrsCard as FSRSCard, reviewDate);
            recordLog = schedulingCards[rating];
            
            // Update the card with new FSRS data
            const updatedCard = convertFSRSToCard(recordLog.card, cardId, currentCard.vocabCard);
            deck.cards[cardIndex] = updatedCard;
            
            transaction.update(deckRef, {
                cards: deck.cards,
                updatedAt: serverTimestamp()
            });
        });
        
        return recordLog!;
    } catch (error) {
        console.error('Error reviewing card:', error);
        throw new Error('Failed to review card');
    }
}


/**
 * Get all possible ratings for a card using FSRS
 */
export function getCardRatings(card: Card, reviewDate: Date = new Date()) {
    const fsrsCard = convertCardToFSRS(card);
    return fsrsScheduler.repeat(fsrsCard as FSRSCard, reviewDate);
}

/**
 * Remove a card from a deck
 */
export async function removeCardFromDeck(
    userId: string, 
    deckId: string, 
    cardId: number
): Promise<void> {
    try {
        await runTransaction(db, async (transaction) => {
            const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
            const deckDoc = await transaction.get(deckRef);
            
            if (!deckDoc.exists()) {
                throw new Error('Deck not found');
            }
            
            const deck = deckDoc.data() as Deck;
            const updatedCards = deck.cards.filter(c => c.id !== cardId);
            
            transaction.update(deckRef, {
                cards: updatedCards,
                updatedAt: serverTimestamp()
            });
        });
    } catch (error) {
        console.error('Error removing card from deck:', error);
        throw new Error('Failed to remove card from deck');
    }
}

/**
 * Get due cards for study session, ordered by difficulty
 */
export function getDueCards(cards: Card[]): Card[] {
    const now = new Date();
    return cards
        .map(convertTimestampToDate)
        .filter(card => (card.fsrsCard.due as Date).getTime() <= now.getTime())
        .sort((a, b) => a.fsrsCard.difficulty - b.fsrsCard.difficulty);
}

/**
 * Sort all cards that is due by difficulty, then all new cards randomly.
 */
export function compareCards(a: Card, b: Card): number {
    const now = new Date();
    const aIsDue = (a.fsrsCard.due as Date).getTime() <= now.getTime();
    const bIsDue = (b.fsrsCard.due as Date).getTime() <= now.getTime();
    
    // Prioritize cards with due dates in the past first
    if (aIsDue && !bIsDue) {
        return -1; // a comes first
    }
    
    if (!aIsDue && bIsDue) {
        return 1; // b comes first
    }
    
    // If both cards are due, compare by difficulty
    if (aIsDue && bIsDue) {
        return a.fsrsCard.difficulty - b.fsrsCard.difficulty;
    }

    // If both cards are not due, return random
    return Math.random() - 0.5;
}


/**
 * Get cards by state
 */
export function getCardsByState(cards: Card[], state: State): Card[] {
  return cards.filter(card => card.fsrsCard.state === state);
}

// ============================================================================
// STUDY SESSION OPERATIONS
// ============================================================================

/**
 * Start a study session
 */
export async function startStudySession(
    userId: string,
    deckId: string
): Promise<string> {
  try {
        const sessionRef = collection(db, 'users', userId, STUDY_SESSIONS_COLLECTION);
        const newSession: Omit<StudySession, 'id'> = {
            deckId,
            cardsReviewed: 0,
            accuracy: 0,
            duration: 0,
            completedAt: serverTimestamp() as Timestamp,
            reviewLogs: []
        };
        
        const docRef = await addDoc(sessionRef, newSession);
        return docRef.id;
    } catch (error) {
        console.error('Error starting study session:', error);
        throw new Error('Failed to start study session');
    }
}

/**
 * Complete a study session with results
 */
export async function completeStudySession(
    userId: string,
    sessionId: string,
    cardsReviewed: number,
    accuracy: number,
    duration: number,
    reviewLogs: any[] = []
): Promise<void> {
    try {
        const sessionRef = doc(db, 'users', userId, STUDY_SESSIONS_COLLECTION, sessionId);
        await updateDoc(sessionRef, {
            cardsReviewed,
            accuracy,
            duration,
            reviewLogs,
            completedAt: serverTimestamp()
        });
    } catch (error) {
        console.error('Error completing study session:', error);
        throw new Error('Failed to complete study session');
    }
}

// ============================================================================
// REAL-TIME SUBSCRIPTIONS
// ============================================================================

/**
 * Subscribe to deck changes
 */
export function subscribeToDeck(
    userId: string, 
    deckId: string, 
    callback: (deck: Deck | null) => void
): Unsubscribe {
    const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
    
    return onSnapshot(deckRef, (doc) => {
        if (doc.exists()) {
        callback({
            id: doc.id,
            ...doc.data()
        } as Deck);
        } else {
        callback(null);
        }
    }, (error) => {
        console.error('Error in deck subscription:', error);
        callback(null);
    });
}

/**
 * Subscribe to user's decks
 */
export function subscribeToUserDecks(
    userId: string, 
    callback: (decks: Deck[]) => void
): Unsubscribe {
    const decksRef = getUserDecksCollection(userId);
    const q = query(decksRef, orderBy('updatedAt', 'desc'));
    
    return onSnapshot(q, (snapshot) => {
        const decks = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
        } as Deck));
        callback(decks);
    }, (error) => {
        console.error('Error in decks subscription:', error);
        callback([]);
    });
}

// ============================================================================
// BATCH OPERATIONS
// ============================================================================

/**
 * Bulk add cards to a deck using FSRS
 */
export async function bulkAddCardsToDeck(
    userId: string, 
    deckId: string, 
    vocabCards: VocabCard[]
): Promise<void> {
    try {
        await runTransaction(db, async (transaction) => {
            const deckRef = doc(db, 'users', userId, DECKS_COLLECTION, deckId);
            const deckDoc = await transaction.get(deckRef);
            
            if (!deckDoc.exists()) {
                throw new Error('Deck not found');
            }
            
            const deck = deckDoc.data() as Deck;
            let nextCardId = Math.max(0, ...deck.cards.map(c => c.id)) + 1;
            
            const newCards: Card[] = vocabCards.map(vocabCard => {
                const fsrsCard = createEmptyCard();
                if (!fsrsCard.last_review) delete fsrsCard.last_review;
                return convertFSRSToCard(fsrsCard, nextCardId++, vocabCard);
            });
            
            transaction.update(deckRef, {
                cards: [...deck.cards, ...newCards],
                updatedAt: serverTimestamp()
            });
        });
    } catch (error) {
        console.error('Error bulk adding cards:', error);
        throw new Error('Failed to bulk add cards');
    }
}

// ============================================================================
// FSRS UTILITIES
// ============================================================================

/**
 * Get FSRS parameters
 */
export function getFSRSParameters(): FSRSParameters {
    return fsrsParams;
}

/**
 * Update FSRS parameters
 */
export function updateFSRSParameters(newParams: Partial<FSRSParameters>): void {
    Object.assign(fsrsParams, newParams);
}

/**
 * Export Rating enum for convenience
 */
export { Rating, State };

/**
 * When reading from Firestore, you might need to convert Timestamp to Date
 * if you need to use Date methods not available on Timestamp
 */
export function convertTimestampToDate(card: Card): Card {
    const updatedFsrsCard = { ...card.fsrsCard };
    let needsUpdate = false;
    
    if (card.fsrsCard.due instanceof Timestamp) {
        updatedFsrsCard.due = card.fsrsCard.due.toDate();
        needsUpdate = true;
    }
    
    if (card.fsrsCard.last_review && card.fsrsCard.last_review instanceof Timestamp) {
        updatedFsrsCard.last_review = card.fsrsCard.last_review.toDate();
        needsUpdate = true;
    }
    
    if (needsUpdate) {
        return {
            ...card,
            fsrsCard: updatedFsrsCard
        };
    }
    
    return card;
} 